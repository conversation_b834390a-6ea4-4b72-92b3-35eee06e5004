#!/usr/bin/env python3
"""
Script para fazer predições de sinais de compra e venda usando modelos XGBoost treinados
Carrega modelos salvos e faz predições para ações específicas
"""

import yfinance as yf
import pandas as pd
import numpy as np
import os
import sys
import pickle
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# Adicionar o diretório src ao path para importar functions e config
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from config_loader import config, setup_environment

def carregar_modelo_multiclasse():
    """
    Carrega o modelo XGBoost multiclasse treinado
    """
    modelo_dir = 'results/models/xgboost_analysis'

    # Carregar modelo multiclasse
    modelo_path = os.path.join(modelo_dir, 'modelo_multiclasse.pkl')
    if not os.path.exists(modelo_path):
        print(f"❌ Modelo multiclasse não encontrado: {modelo_path}")
        return None

    with open(modelo_path, 'rb') as f:
        dados_modelo = pickle.load(f)

    print(f"✅ Modelo multiclasse carregado:")
    print(f"   • Acurácia: {dados_modelo['accuracy']:.3f}")
    print(f"   • Classes: {dados_modelo['classes']}")
    print(f"   • Features: {len(dados_modelo['feature_cols'])}")

    return dados_modelo

def baixar_dados_acao(ticker):
    """
    Baixa dados históricos recentes de uma ação
    """
    try:
        print(f"📊 Baixando dados de {ticker}...")
        
        # Baixar dados dos últimos 3 meses para ter dados suficientes
        dados = yf.download(ticker, period='3mo', progress=False)
        
        if dados.empty:
            print(f"❌ Nenhum dado encontrado para {ticker}")
            return None
        
        # Corrigir MultiIndex se necessário
        if isinstance(dados.columns, pd.MultiIndex):
            dados.columns = dados.columns.droplevel(1)
            
        return dados
        
    except Exception as e:
        print(f"❌ Erro ao baixar dados de {ticker}: {e}")
        return None

def preparar_features(dados):
    """
    Prepara features para predição seguindo o mesmo padrão do treinamento usando configuração
    """
    # Calcular média inteligente:
    # - Dias anteriores: usar OHLC completo (Close disponível)
    # - Dia atual: usar OHL (para trading intraday)
    dados['Media_OHLC'] = (dados['Open'] + dados['Close'] + dados['Low'] + dados['High']) / 4

    # Para o último dia (dia atual), usar média OHL se Close não estiver disponível
    # ou se for para trading intraday
    ultimo_indice = len(dados) - 1
    close_ultimo_dia = dados['Close'].iloc[ultimo_indice]

    # Verificar se é Series e extrair valor escalar
    if hasattr(close_ultimo_dia, 'item'):
        close_valor = close_ultimo_dia.item()
    else:
        close_valor = close_ultimo_dia

    if pd.isna(close_valor) or close_valor == 0:
        dados['Media_OHLC'].iloc[ultimo_indice] = (
            dados['Open'].iloc[ultimo_indice] +
            dados['High'].iloc[ultimo_indice] +
            dados['Low'].iloc[ultimo_indice]
        ) / 3

    # Calcular pct_change da média OHLC (variação percentual em relação ao dia anterior)
    dados['Media_OHLC_PctChange'] = dados['Media_OHLC'].pct_change()

    # Usar configurações do XGBoost
    volatility_window = config.get('xgboost.features.volatility_window')
    spread_multiplier = config.get('xgboost.features.spread_multiplier')
    ohlc_lags = config.get('xgboost.features.ohlc_lags')

    # Calcular volatilidade usando janela configurada
    returns = dados['Media_OHLC'].pct_change()
    dados['Volatilidade'] = returns.rolling(window=volatility_window).std() * 100

    # Calcular spread estimado usando multiplicador configurado
    dados['Spread'] = dados['Volatilidade'] * spread_multiplier

    # Preencher NaN
    dados['Spread'] = dados['Spread'].fillna(dados['Spread'].mean())
    dados['Volatilidade'] = dados['Volatilidade'].fillna(dados['Volatilidade'].mean())

    # Criar features de pct_change da média OHLC passada usando configuração
    for i in range(1, ohlc_lags + 1):
        dados[f'Media_OHLC_PctChange_Lag_{i}'] = dados['Media_OHLC_PctChange'].shift(i)

    # Adicionar features one-hot encoding para dias da semana (segunda a sexta)
    # Monday=0, Tuesday=1, Wednesday=2, Thursday=3, Friday=4, Saturday=5, Sunday=6
    weekday = dados.index.dayofweek
    dados['Segunda'] = (weekday == 0).astype(int)  # Monday
    dados['Terca'] = (weekday == 1).astype(int)    # Tuesday
    dados['Quarta'] = (weekday == 2).astype(int)   # Wednesday
    dados['Quinta'] = (weekday == 3).astype(int)   # Thursday
    dados['Sexta'] = (weekday == 4).astype(int)    # Friday

    # Adicionar features one-hot encoding para meses (1 a 12)
    month = dados.index.month
    for i in range(1, 13):
        dados[f'Mes_{i}'] = (month == i).astype(int)

    # Remover linhas com NaN
    dados = dados.dropna()

    return dados

def fazer_predicao(ticker, dados_modelo):
    """
    Faz predição de sinais para uma ação específica usando modelo multiclasse
    """
    # Baixar dados
    dados = baixar_dados_acao(ticker)
    if dados is None:
        return None

    # Preparar features
    dados_processados = preparar_features(dados)
    if len(dados_processados) == 0:
        print(f"❌ Dados insuficientes para {ticker}")
        return None

    # Extrair features do último dia
    feature_cols = dados_modelo['feature_cols']
    ultimo_dia = dados_processados.iloc[-1]

    # Verificar se todas as features estão disponíveis
    features_disponiveis = [col for col in feature_cols if col in dados_processados.columns]
    if len(features_disponiveis) != len(feature_cols):
        print(f"⚠️ Algumas features não disponíveis para {ticker}")
        return None

    X = ultimo_dia[feature_cols].values.reshape(1, -1)

    # Normalizar usando o scaler treinado se disponível
    if dados_modelo['scaler'] is not None:
        X_scaled = dados_modelo['scaler'].transform(X)
    else:
        X_scaled = X

    # Fazer predições multiclasse
    pred_multiclass = dados_modelo['modelo'].predict(X_scaled)[0]
    prob_multiclass = dados_modelo['modelo'].predict_proba(X_scaled)[0]

    # Converter para formato binário
    pred_compra = 1 if pred_multiclass == 1 else 0
    pred_venda = 1 if pred_multiclass == 2 else 0

    # Probabilidades por classe: [Sem ação, Compra, Venda]
    prob_sem_acao = prob_multiclass[0]
    prob_compra = prob_multiclass[1]
    prob_venda = prob_multiclass[2]

    # Obter dados atuais
    preco_atual = ultimo_dia['Media_OHLC']
    volume_atual = ultimo_dia['Volume']
    volatilidade_atual = ultimo_dia['Volatilidade']

    resultado = {
        'ticker': ticker,
        'data': dados_processados.index[-1].strftime('%Y-%m-%d'),
        'preco_atual': preco_atual,
        'volume': volume_atual,
        'volatilidade': volatilidade_atual,
        'pred_multiclass': pred_multiclass,
        'sinal_compra': pred_compra,
        'sinal_venda': pred_venda,
        'prob_sem_acao': prob_sem_acao,
        'prob_compra': prob_compra,
        'prob_venda': prob_venda,
        'confianca_compra': prob_compra,
        'confianca_venda': prob_venda
    }

    return resultado

def main():
    """
    Função principal
    """
    # Configurar ambiente
    setup_environment()
    
    print("🔮 PREDIÇÃO DE SINAIS - XGBOOST")
    print("=" * 50)
    
    # Carregar modelo multiclasse
    dados_modelo = carregar_modelo_multiclasse()
    if dados_modelo is None:
        print("❌ Erro ao carregar modelo. Execute primeiro o script de treinamento.")
        return
    
    # Lista de ações para predição (algumas das ações diversificadas)
    acoes_teste = [
        'TIMS3.SA', 'WEGE3.SA', 'STBP3.SA', 'SYNE3.SA', 'LVTC3.SA'
    ]
    
    print(f"\n🎯 Fazendo predições para {len(acoes_teste)} ações...")
    print("=" * 50)
    
    resultados = []
    
    for ticker in acoes_teste:
        try:
            resultado = fazer_predicao(ticker, dados_modelo)
            if resultado:
                resultados.append(resultado)

                # Mostrar resultado
                print(f"\n📊 {ticker} ({resultado['data']}):")
                print(f"   💰 Preço: R$ {resultado['preco_atual']:.2f}")
                print(f"   📈 Volume: {resultado['volume']:,.0f}")
                print(f"   📊 Volatilidade: {resultado['volatilidade']:.2f}%")

                # Mostrar predição multiclasse
                classes = ['Sem Ação', 'Compra', 'Venda']
                pred_class = classes[resultado['pred_multiclass']]
                print(f"   🎯 Predição: {pred_class}")
                print(f"   📊 Probabilidades:")
                print(f"      • Sem Ação: {resultado['prob_sem_acao']:.1%}")
                print(f"      • Compra: {resultado['prob_compra']:.1%}")
                print(f"      • Venda: {resultado['prob_venda']:.1%}")

                # Sinais binários
                if resultado['sinal_compra'] == 1:
                    print(f"   🟢 SINAL DE COMPRA (Conf: {resultado['confianca_compra']:.1%})")
                elif resultado['sinal_venda'] == 1:
                    print(f"   🔴 SINAL DE VENDA (Conf: {resultado['confianca_venda']:.1%})")
                else:
                    print(f"   ⚪ SEM AÇÃO")

        except Exception as e:
            print(f"❌ Erro ao processar {ticker}: {e}")
    
    # Resumo
    if resultados:
        print(f"\n📋 RESUMO DAS PREDIÇÕES")
        print("=" * 50)
        
        sinais_compra = [r for r in resultados if r['sinal_compra'] == 1]
        sinais_venda = [r for r in resultados if r['sinal_venda'] == 1]
        
        print(f"🟢 Sinais de COMPRA: {len(sinais_compra)}")
        for r in sinais_compra:
            print(f"   • {r['ticker']} - Prob: {r['prob_compra']:.1%}")
        
        print(f"\n🔴 Sinais de VENDA: {len(sinais_venda)}")
        for r in sinais_venda:
            print(f"   • {r['ticker']} - Prob: {r['prob_venda']:.1%}")
        
        # Salvar resultados
        df_resultados = pd.DataFrame(resultados)
        resultado_path = 'results/predicoes_sinais_xgboost.csv'
        df_resultados.to_csv(resultado_path, index=False)
        print(f"\n💾 Resultados salvos em: {resultado_path}")
    
    print(f"\n✅ Predições concluídas!")

if __name__ == "__main__":
    main()
